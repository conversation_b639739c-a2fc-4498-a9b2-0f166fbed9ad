import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import type { AIProvider } from '@/hooks/useAIChat'
import { useAIChatStore } from '@/hooks/useAIChat'
import { useModelStore } from '@/hooks/useModelStore'
import { useToast } from '@/hooks/useToast'
import { CheckIcon, Eye, EyeOff, RefreshCw, Search, X, AlertCircle } from 'lucide-react'
import React, { memo, useCallback, useState } from 'react'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { providers } from 'shared/providers'
import {
  classifyModelsByVendor,
  getVendorsWithModels,
  VENDOR_INFO
} from '@/utils/modelClassification'

const CustomModelInput = memo(
  ({
    model,
    onChange,
  }: { model: string; onChange: (model: string) => void }) => {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">自定义模型</CardTitle>
          <CardDescription>
            输入您要使用的模型名称
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label>模型名称</Label>
            <Input
              value={model}
              onChange={e => onChange(e.target.value)}
              placeholder="model，如 deepseek-reasoner"
              className="font-mono"
            />
          </div>
        </CardContent>
      </Card>
    )
  },
)

const VolcengineEndpoint = memo(
  ({
    model,
    onChange,
  }: { model: string; onChange: (model: string) => void }) => {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">火山引擎模型</CardTitle>
          <CardDescription>
            输入模型ID或接入点ID
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label>模型或接入点 ID</Label>
            <Input
              value={model}
              onChange={e => onChange(e.target.value)}
              placeholder="请输入火山引擎的模型 ID 或接入点 ID"
              className="font-mono"
            />
            <p className="text-xs text-muted-foreground">
              您可以在
              <a
                href="https://www.volcengine.com/docs/82379/1330310"
                rel="noreferrer"
                target="_blank"
                className="px-1 text-primary hover:underline"
              >
                模型列表
              </a>
              获取模型 ID，或在
              <a
                href="https://console.volcengine.com/ark/region:ark+cn-beijing/endpoint"
                rel="noreferrer"
                target="_blank"
                className="px-1 text-primary hover:underline"
              >
                火山引擎控制台
              </a>
              获取接入点 ID。
            </p>
          </div>
        </CardContent>
      </Card>
    )
  },
)

const ModelSelector = memo(
  ({
    provider,
    model,
    onChange,
    apiKey,
    customBaseURL,
  }: {
    provider: keyof typeof providers
    model: string
    onChange: (model: string) => void
    apiKey: string
    customBaseURL?: string
  }) => {
    const { toast } = useToast()
    const [fetchingModels, setFetchingModels] = useState(false)
    const [activeVendor, setActiveVendor] = useState<string>('')
    const [searchQuery, setSearchQuery] = useState('')
    const {
      getModelsForProvider,
      setModelsForProvider,
      hasModelsForProvider,
      getLastUpdated
    } = useModelStore()

    // 获取缓存的模型列表或默认模型列表
    const cachedModels = getModelsForProvider(provider)
    const modelList = cachedModels.length > 0 ? cachedModels : [...providers[provider].models]
    const lastUpdated = getLastUpdated(provider)

    // 搜索过滤
    const filteredModelList = React.useMemo(() => {
      if (!searchQuery.trim()) return modelList
      return modelList.filter(modelName =>
        modelName.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }, [modelList, searchQuery])

    // 按厂商分类模型
    const modelsByVendor = classifyModelsByVendor(filteredModelList)
    const vendors = getVendorsWithModels(modelsByVendor)

    // 设置默认选中的厂商
    React.useEffect(() => {
      if (vendors.length > 0 && !activeVendor) {
        // 如果当前选中的模型属于某个厂商，优先选中该厂商
        if (model) {
          const currentModelVendor = Object.keys(modelsByVendor).find(vendor =>
            modelsByVendor[vendor].includes(model)
          )
          if (currentModelVendor) {
            setActiveVendor(currentModelVendor)
            return
          }
        }
        // 否则选中第一个厂商
        setActiveVendor(vendors[0])
      }
    }, [vendors, activeVendor, model, modelsByVendor])

    // 检查是否支持模型拉取（volcengine使用自定义endpoint，不支持标准模型列表API）
    const supportsModelFetch = provider !== 'volcengine'

    const handleFetchModels = useCallback(async () => {
      if (!apiKey) {
        toast.error('请先输入 API Key')
        return
      }

      setFetchingModels(true)
      try {
        const models = await window.ipcRenderer.invoke(
          IPC_CHANNELS.tasks.aiChat.fetchModels,
          {
            apiKey,
            provider,
            customBaseURL: provider === 'custom' ? customBaseURL : undefined,
          }
        )

        setModelsForProvider(provider, models)
        toast.success(`成功拉取到 ${models.length} 个模型`)
      } catch (error) {
        console.error('拉取模型失败:', error)
        toast.error(error instanceof Error ? error.message : '拉取模型失败：未知错误')
      } finally {
        setFetchingModels(false)
      }
    }, [apiKey, provider, customBaseURL, toast, setModelsForProvider])

    // 处理特殊的provider
    if (provider === 'custom') {
      return (
        <CustomModelInput
          model={model}
          onChange={onChange}
        />
      )
    }

    if (provider === 'volcengine') {
      return (
        <VolcengineEndpoint
          model={model}
          onChange={onChange}
        />
      )
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>选择模型</Label>
          <div className="flex items-center gap-2">
            {lastUpdated && (
              <span className="text-xs text-muted-foreground">
                更新于 {new Date(lastUpdated).toLocaleString()}
              </span>
            )}
            {supportsModelFetch && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleFetchModels}
                disabled={fetchingModels || !apiKey}
                className="h-7 px-2"
              >
                <RefreshCw className={`w-3 h-3 mr-1 ${fetchingModels ? 'animate-spin' : ''}`} />
                拉取模型
              </Button>
            )}
          </div>
        </div>

        {/* 当前选中的模型 */}
        {model && (
          <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg border">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-primary rounded-full" />
              <span className="text-sm font-medium">当前选中:</span>
              <code className="text-sm font-mono bg-background px-2 py-1 rounded border">
                {model}
              </code>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => {
                // 找到当前模型所属的厂商并切换到该厂商
                const currentModelVendor = Object.keys(modelsByVendor).find(vendor =>
                  modelsByVendor[vendor].includes(model)
                )
                if (currentModelVendor && vendors.length > 1) {
                  setActiveVendor(currentModelVendor)
                }

                // 滚动到对应的模型项
                setTimeout(() => {
                  const modelElement = document.getElementById(model)
                  if (modelElement) {
                    modelElement.scrollIntoView({
                      behavior: 'smooth',
                      block: 'center'
                    })
                    // 添加高亮效果
                    modelElement.style.backgroundColor = 'hsl(var(--primary) / 0.1)'
                    setTimeout(() => {
                      modelElement.style.backgroundColor = ''
                    }, 2000)
                  }
                }, 100)
              }}
              className="h-7 px-2 text-xs"
            >
              定位
            </Button>
          </div>
        )}

        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索模型...（如：free）"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-10"
          />
          {searchQuery && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setSearchQuery('')}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {filteredModelList.length === 0 ? (
          <Card>
            <CardContent className="py-8">
              <div className="text-center text-muted-foreground">
                <Search className="mx-auto h-8 w-8 mb-2" />
                <p>没有找到匹配的模型</p>
                {searchQuery && (
                  <p className="text-sm mt-1">
                    尝试搜索其他关键词或{' '}
                    <button
                      onClick={() => setSearchQuery('')}
                      className="text-primary hover:underline"
                    >
                      清空搜索
                    </button>
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        ) : vendors.length > 1 ? (
          <Tabs value={activeVendor} onValueChange={setActiveVendor} className="w-full">
            <TabsList className={`grid w-full gap-1 ${vendors.length <= 2 ? 'grid-cols-2' :
              vendors.length <= 3 ? 'grid-cols-3' :
                vendors.length <= 4 ? 'grid-cols-4' :
                  vendors.length <= 5 ? 'grid-cols-5' :
                    'grid-cols-6'
              }`}>
              {vendors.map(vendor => (
                <TabsTrigger key={vendor} value={vendor} className="flex items-center gap-1">
                  <div className={`w-2 h-2 rounded-full ${VENDOR_INFO[vendor]?.color || 'bg-gray-500'}`} />
                  {VENDOR_INFO[vendor]?.name || vendor}
                  <Badge variant="secondary" className="ml-1">
                    {modelsByVendor[vendor].length}
                  </Badge>
                </TabsTrigger>
              ))}
            </TabsList>

            {vendors.map(vendor => (
              <TabsContent key={vendor} value={vendor} className="mt-8">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${VENDOR_INFO[vendor]?.color || 'bg-gray-500'}`} />
                      {VENDOR_INFO[vendor]?.name || vendor} 模型
                      <Badge variant="secondary">
                        {modelsByVendor[vendor].length}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <RadioGroup value={model} onValueChange={onChange} className="space-y-2">
                      {modelsByVendor[vendor].map(modelName => (
                        <div key={modelName} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                          <RadioGroupItem value={modelName} id={modelName} />
                          <Label
                            htmlFor={modelName}
                            className="flex-1 cursor-pointer text-sm font-mono"
                          >
                            {modelName}
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        ) : (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">
                模型列表
                <Badge variant="secondary" className="ml-2">
                  {filteredModelList.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <RadioGroup value={model} onValueChange={onChange} className="space-y-2">
                {filteredModelList.map(modelName => (
                  <div key={modelName} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                    <RadioGroupItem value={modelName} id={modelName} />
                    <Label
                      htmlFor={modelName}
                      className="flex-1 cursor-pointer text-sm font-mono"
                    >
                      {modelName}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </CardContent>
          </Card>
        )}

        {hasModelsForProvider(provider) && (
          <p className="text-xs text-muted-foreground">
            显示缓存的模型列表，点击"拉取模型"获取最新列表
          </p>
        )}
      </div>
    )
  },
)

const ApiKeyInput = memo(
  ({
    provider,
    apiKey,
    onChange,
    onTest,
    testSuccess,
    testLoading,
  }: {
    provider: AIProvider
    apiKey: string
    onChange: (key: string) => void
    onTest: () => void
    testSuccess: boolean
    testLoading: boolean
  }) => {
    const isCustom = provider === 'custom'
    const providerInfo = providers[provider as keyof typeof providers]
    const [showPassword, setShowPassword] = useState(false)

    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>API Key</Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onTest}
            disabled={!apiKey || testLoading}
            className="h-7 px-2"
          >
            {testLoading ? (
              <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
            ) : testSuccess ? (
              <CheckIcon className="w-3 h-3 mr-1 text-green-600" />
            ) : null}
            {testLoading ? '测试中...' : testSuccess ? '测试通过' : '测试连接'}
          </Button>
        </div>
        <div className="relative w-full">
          <Input
            type={showPassword ? 'text' : 'password'}
            value={apiKey}
            onChange={e => onChange(e.target.value)}
            placeholder={`请输入您的 ${providerInfo?.name || '自定义服务'} API Key`}
            className="font-mono pr-10"
          />
          <Button
            type="button"
            variant="link"
            size="icon"
            onClick={() => setShowPassword(prev => !prev)}
            className="absolute right-1 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
          >
            {showPassword ? (
              <EyeOff className="w-4 h-4" />
            ) : (
              <Eye className="w-4 h-4" />
            )}
          </Button>
        </div>
        {!isCustom && providerInfo && (
          <p className="text-xs text-muted-foreground">
            您可以在
            <a
              href={providerInfo.apiUrl}
              rel="noreferrer"
              target="_blank"
              className="px-1 text-primary hover:underline"
            >
              {providerInfo.name}
            </a>
            获取 API Key。您的密钥将被安全地存储在本地。
          </p>
        )}
        {isCustom && (
          <p className="text-xs text-muted-foreground">
            您的密钥将被安全地存储在本地。
          </p>
        )}
      </div>
    )
  },
)

export default function APIKeySettings() {
  const {
    apiKeys,
    config,
    setConfig,
    setApiKey,
    customBaseURL,
    setCustomBaseURL,
  } = useAIChatStore()
  const { toast } = useToast()
  const { clearModelsForProvider } = useModelStore()

  const [testLoading, setTestLoading] = useState(false)
  const [testSuccess, setTestSuccess] = useState(false)

  // 检查是否有选择模型
  const hasSelectedModel = !!config.model
  
  // 检查是否有配置API Key
  const hasApiKey = !!apiKeys[config.provider]
  
  // 判断是否需要显示提示
  const showConfigWarning = !hasSelectedModel || !hasApiKey

  const handleProviderChange = useCallback((value: string) => {
    const provider = value as AIProvider
    setConfig({
      provider,
      model: config.modelPreferences[provider] || '',
    })
    setTestSuccess(false)
  }, [config.modelPreferences, setConfig])

  const handleModelChange = useCallback((model: string) => {
    setConfig({
      modelPreferences: {
        ...config.modelPreferences,
        [config.provider]: model,
      },
      model,
    })
  }, [config.modelPreferences, config.provider, setConfig])

  const handleApiKeyChange = useCallback((key: string) => {
    setApiKey(config.provider, key)
    setTestSuccess(false)
  }, [config.provider, setApiKey])

  const handleTestApiKey = useCallback(async () => {
    const apiKey = apiKeys[config.provider]
    if (!apiKey) {
      toast.error('请先输入 API Key')
      return
    }

    setTestLoading(true)
    try {
      const result = await window.ipcRenderer.invoke(IPC_CHANNELS.tasks.aiChat.testApiKey, {
        apiKey,
        provider: config.provider,
        customBaseURL: config.provider === 'custom' ? customBaseURL : undefined,
      })

      if (result.success) {
        setTestSuccess(true)
        toast.success('API Key 测试通过！')
      } else {
        setTestSuccess(false)
        toast.error(result.error || 'API Key 测试失败')
      }
    } catch (error) {
      setTestSuccess(false)
      toast.error(error instanceof Error ? error.message : 'API Key 测试失败')
    } finally {
      setTestLoading(false)
    }
  }, [apiKeys, config.provider, customBaseURL, toast])

  const handleClearModels = useCallback(() => {
    clearModelsForProvider(config.provider)
    toast.success('已清空缓存的模型列表')
  }, [config.provider, clearModelsForProvider, toast])

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">API Key 配置</h3>
        <p className="text-sm text-muted-foreground">
          配置AI服务提供商的API密钥和模型设置
        </p>
      </div>
      
      {showConfigWarning && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>配置提醒</AlertTitle>
          <AlertDescription>
            {hasSelectedModel 
              ? "您已选择模型但尚未配置 API Key，请在下方输入您的 API Key 并测试连接。" 
              : "您尚未选择模型或配置 API Key，请先选择服务提供商、模型并配置相应的 API Key。"}
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>服务提供商设置</CardTitle>
          <CardDescription>
            选择AI服务提供商并配置相应的API密钥
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label>选择提供商</Label>
            <Select
              value={config.provider}
              onValueChange={handleProviderChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择服务商" />
              </SelectTrigger>
              <SelectContent>
                {(
                  Object.keys(providers) as Array<keyof typeof providers>
                ).map(key => (
                  <SelectItem key={key} value={key}>
                    {providers[key].name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {config.provider === 'custom' && (
            <div className="space-y-2">
              <Label>Base URL</Label>
              <Input
                value={customBaseURL}
                onChange={e => setCustomBaseURL(e.target.value)}
                placeholder="baseURL，如 https://api.example.com/v1"
                className="font-mono"
              />
            </div>
          )}

          <ApiKeyInput
            provider={config.provider}
            apiKey={apiKeys[config.provider] || ''}
            onChange={handleApiKeyChange}
            onTest={handleTestApiKey}
            testSuccess={testSuccess}
            testLoading={testLoading}
          />

          <ModelSelector
            provider={config.provider as keyof typeof providers}
            model={config.modelPreferences[config.provider] || ''}
            onChange={handleModelChange}
            apiKey={apiKeys[config.provider] || ''}
            customBaseURL={customBaseURL}
          />

          <div className="flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearModels}
            >
              清空模型缓存
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
