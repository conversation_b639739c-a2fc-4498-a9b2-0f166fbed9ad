# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## language
chinese communication.

## Project Overview

This is an Electron-based desktop application for live streaming assistance, primarily targeting Chinese live streaming platforms like Douyin (TikTok), Xiaohongshu (Little Red Book), WeChat Channels, Kuaishou, etc. The application provides automation features for live streaming including automatic messaging, pop-ups, replies, and voice output.

## Architecture

The application follows a standard Electron architecture with separate main and renderer processes:

- **Main Process** (`electron/main/`): Handles system-level operations, browser automation with Playwright, file system operations, and IPC communication
- **Renderer Process** (`src/`): React-based UI with TypeScript
- **Shared Code** (`shared/`): IPC channel definitions and shared types
- **Preload Scripts** (`electron/preload/`): Secure bridge between main and renderer processes

### Key Components

1. **Live Control System**: Connects to live streaming platform control panels and automates interactions
2. **Auto Message**: Sends automated messages at scheduled intervals
3. **Auto Pop-up**: Automatically displays product information pop-ups
4. **Auto Reply**: AI-powered comment reply system with keyword matching
5. **Voice Output**: Text-to-speech functionality with dual-track audio support
6. **AI Chat**: Integration with various AI providers (DeepSeek, OpenRouter, SiliconFlow, VolcEngine)
7. **Video Processing**: FFmpeg-based video cutting, audio separation, and video concatenation tools

## Development Commands

### Install Dependencies
```bash
pnpm install
```

### Development
```bash
pnpm dev
```

### Build
```bash
pnpm build
```

### Testing
```bash
pnpm test
```

Note: Currently there are no test files in the project. Tests would need to be written following the vitest configuration.

## Code Structure

### Main Process Structure
- `electron/main/index.ts`: Main Electron entry point
- `electron/main/ipc/`: IPC handlers for communication with renderer
- `electron/main/tasks/`: Task implementations (autoMessage, autoPopUp, autoReply, etc.)
- `electron/main/services/`: Core services (AIChat, WebSocket)
- `electron/main/managers/`: State managers (Account, BrowserContext, Task)
- `electron/main/connection/`: Live streaming platform connection adapters

### Renderer Process Structure
- `src/App.tsx`: Main application component with global IPC listeners
- `src/pages/`: Page components for different features
- `src/hooks/`: Custom React hooks for state management
- `src/components/`: Reusable UI components
- `src/router/`: React Router configuration
- `src/utils/`: Utility functions
- `src/services/`: Client-side services

### IPC Communication
The application uses a structured IPC channel system defined in `shared/ipcChannels.ts`. All communication between main and renderer processes goes through these predefined channels.

### Video Processing
Video processing functionality is implemented with FFmpeg and includes:
- Video cutting by time segments
- Audio separation from video
- Video extraction (removing audio)
- Video concatenation
- File management for ComfyUI integration

## Key Dependencies

- **Electron**: Desktop application framework
- **React**: UI library with React Router for navigation
- **Playwright**: Browser automation for connecting to live streaming platforms
- **FFmpeg**: Video and audio processing
- **OpenAI**: AI provider integration
- **Tailwind CSS**: Styling framework with Radix UI components
- **Zustand**: State management
- **Biome**: Code formatting and linting

## Important Notes

1. The application uses Playwright for browser automation to interact with live streaming platform control panels
2. Video processing requires FFmpeg to be installed on the system
3. AI features require API keys for various providers
4. The application supports multiple live streaming platforms through adapter patterns
5. Dual-track audio system allows separate audio playback for host and assistant
6. All configuration is account-based, supporting multiple accounts