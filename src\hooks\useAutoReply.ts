import { EVENTS, eventEmitter } from '@/utils/events'
import { type StringFilterConfig, matchObject } from '@/utils/filter'
import { autoReplyLogger } from '@/utils/logger'
import { mergeWithoutArray } from '@/utils/misc'
import { useMemoizedFn } from 'ahooks'
import { useMemo } from 'react'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { useAIChatStore } from './useAIChat'
import { useAccounts } from './useAccounts'
import { useCurrentLiveControl } from './useLiveControl'

// 用户昵称选项管理
interface NicknameOption {
  id: string
  name: string
  isDefault: boolean
}

interface NicknameOptionsStore {
  options: NicknameOption[]
  addOption: (name: string) => void
  removeOption: (id: string) => void
  setDefault: (id: string) => void
  getDefaultOption: () => NicknameOption | null
}

export const useNicknameOptionsStore = create<NicknameOptionsStore>()(
  persist(
    (set, get) => ({
      options: [
        { id: 'default', name: '游客', isDefault: true }
      ],
      addOption: (name: string) => {
        const trimmedName = name.trim()
        if (!trimmedName) return

        set(state => {
          // 检查是否已存在相同名称
          const exists = state.options.some(option => option.name === trimmedName)
          if (exists) return state

          return {
            options: [
              ...state.options,
              {
                id: crypto.randomUUID(),
                name: trimmedName,
                isDefault: false
              }
            ]
          }
        })
      },
      removeOption: (id: string) => {
        set(state => {
          const optionToRemove = state.options.find(option => option.id === id)
          if (!optionToRemove) return state

          const newOptions = state.options.filter(option => option.id !== id)

          // 如果删除的是默认选项，且还有其他选项，则设置第一个为默认
          if (optionToRemove.isDefault && newOptions.length > 0) {
            newOptions[0].isDefault = true
          }

          return { options: newOptions }
        })
      },
      setDefault: (id: string) => {
        set(state => ({
          options: state.options.map(option => ({
            ...option,
            isDefault: option.id === id
          }))
        }))
      },
      getDefaultOption: () => {
        const state = get()
        return state.options.find(option => option.isDefault) || null
      }
    }),
    {
      name: 'nickname-options-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
)

type DeepPartial<T> = T extends (...args: unknown[]) => unknown
  ? T
  : T extends Array<infer U>
    ? Array<DeepPartial<U>>
    : T extends object
      ? {
          [P in keyof T]?: DeepPartial<T[P]>
        }
      : T

interface ReplyPreview {
  id: string
  commentId: string
  replyContent: string
  replyFor: string
  time: string
  type: 'keyword' | 'ai' | 'manual' // 回复类型
  status: 'pending' | 'sending' | 'sent' | 'failed' // 发送状态
  error?: string // 错误信息
  autoSent?: boolean // 是否自动发送
}

export type MessageType =
  | 'comment'
  | 'room_enter'
  | 'room_like'
  | 'room_follow'
  | 'subscribe_merchant_brand_vip'
  | 'live_order'
  | 'ecom_fansclub_participate'

export type EventMessageType = Exclude<MessageType, 'comment'>

export type Message = DouyinLiveMessage
export type MessageOf<T extends MessageType> = Extract<Message, { msg_type: T }>

type ListeningStatus = 'waiting' | 'listening' | 'stopped' | 'error'

interface AutoReplyContext {
  isRunning: boolean
  isListening: ListeningStatus
  replies: ReplyPreview[]
  comments: Message[]
  config: AutoReplyConfig
}

interface AutoReplyBaseConfig {
  entry: 'control' | 'compass' // 中控台 | 电商罗盘
  hideUsername: boolean
  hideHost: boolean
  liveMode: 'voice' | 'digital-human' // 直播模式：语音直播 | 数字人直播
  assistantEnabled: boolean // 助理开关：控制AI回复功能
  assistantMode: 'voice' | 'digital-human' // 助理模式：语音助理 | 数字人助理
  comment: {
    keywordReply: {
      enable: boolean
      autoSend: boolean
      autoSendToVoice: boolean
      rules: {
        keywords: string[]
        contents: string[]
      }[]
    }
    aiReply: {
      enable: boolean
      prompt: string
      context: string
      contextByScriptList: Record<string, string> // 按话术列表ID存储的知识库信息
      autoSend: boolean
      autoSendToVoice: boolean
    }
  }
  blockList: string[]
  ws?: {
    enable: boolean
    port: number
  }
}

export type SimpleEventReplyMessage =
  | string
  | { content: string; filter: StringFilterConfig }

export interface SimpleEventReply {
  enable: boolean
  messages: SimpleEventReplyMessage[]
  options?: Record<string, boolean | number>
}

type EventBasedReplies = {
  [K in EventMessageType]: SimpleEventReply
}

type PassiveInteractionReplies = {
  [K in EventMessageType]: SimpleEventReply
}

export type AutoReplyConfig = AutoReplyBaseConfig & EventBasedReplies & {
  passiveInteraction: PassiveInteractionReplies
}

interface AutoReplyState {
  contexts: Record<string, AutoReplyContext>
}
interface AutoReplyAction {
  setIsRunning: (accountId: string, isRunning: boolean) => void
  setIsListening: (accountId: string, isListening: ListeningStatus) => void
  addComment: (accountId: string, comment: Message) => void
  addReply: (
    accountId: string,
    commentId: string,
    nickname: string,
    content: string,
    type?: ReplyPreview['type'],
    autoSent?: boolean,
  ) => string
  updateReplyStatus: (
    accountId: string,
    replyId: string,
    status: ReplyPreview['status'],
    error?: string,
  ) => void
  removeReply: (accountId: string, commentId: string) => void
  clearComments: (accountId: string) => void
  clearReplies: (accountId: string) => void

  updateConfig: (
    accountId: string,
    configUpdates: DeepPartial<AutoReplyConfig>,
  ) => void
}

const defaultPrompt = `你是一个直播带货主播，负责回复观众的评论。请参考下面的要求、产品介绍和直播商品等，用简短友好的语气回复，一定一定不要超过45个字。

## 要求
- 不要@游客或用户昵称，直接回复即可。
- 不要包含表情等特殊字符。

当前直播间信息：
- 直播间类型：带货直播
- 主播风格：亲切友好，活泼热情
- 主要商品：儿童玩具
- 观众群体：有孩子的家庭

常见互动场景：
1. 观众询问商品信息
2. 观众夸奖主播或商品
3. 观众要求展示商品细节
4. 观众询问价格和优惠
5. 观众分享购买体验
6. 日常闲聊和互动

注意事项：
- 避免回复无关内容、广告、恶意评论
- 对于不合适的内容直接返回 false
- 保持专业但不失亲和力的态度`

const defaultContext = `当前直播间信息：
- 直播间类型：带货直播
- 主播风格：亲切友好，活泼热情
- 主要商品：时尚服装、美妆用品
- 观众群体：年轻女性为主

常见互动场景：
1. 观众询问商品信息
2. 观众夸奖主播或商品
3. 观众要求展示商品细节
4. 观众询问价格和优惠
5. 观众分享购买体验
6. 日常闲聊和互动

注意事项：
- 避免回复无关内容、广告、恶意评论
- 对于不合适的内容直接返回 false
- 保持专业但不失亲和力的态度`

const createDefaultConfig = (): AutoReplyConfig => {
  return {
    entry: 'control',
    hideUsername: false,
    hideHost: false,
    liveMode: 'voice', // 默认为语音直播模式
    assistantEnabled: false, // 默认关闭助理功能
    assistantMode: 'voice', // 默认为语音助理模式
    comment: {
      keywordReply: {
        enable: false,
        autoSend: false,
        autoSendToVoice: true,
        rules: [],
      },
      aiReply: {
        enable: false,
        prompt: defaultPrompt,
        context: defaultContext,
        contextByScriptList: {}, // 初始化为空对象
        autoSend: false,
        autoSendToVoice: true,
      },
    },
    room_enter: {
      enable: false,
      messages: [],
    },
    room_like: {
      enable: false,
      messages: [],
    },
    subscribe_merchant_brand_vip: {
      enable: false,
      messages: [],
    },
    live_order: {
      enable: false,
      messages: [],
      options: {
        onlyReplyPaid: false,
      },
    },
    room_follow: {
      enable: false,
      messages: [],
    },
    ecom_fansclub_participate: {
      enable: false,
      messages: [],
    },
    passiveInteraction: {
      room_enter: {
        enable: false,
        messages: [],
        options: {
          intervalMinSeconds: 30,
          intervalMaxSeconds: 60,
        },
      },
      room_like: {
        enable: false,
        messages: [],
        options: {
          intervalMinSeconds: 30,
          intervalMaxSeconds: 60,
        },
      },
      subscribe_merchant_brand_vip: {
        enable: false,
        messages: [],
        options: {
          intervalMinSeconds: 30,
          intervalMaxSeconds: 60,
        },
      },
      live_order: {
        enable: false,
        messages: [],
        options: {
          onlyReplyPaid: false,
          intervalMinSeconds: 30,
          intervalMaxSeconds: 60,
        },
      },
      room_follow: {
        enable: false,
        messages: [],
        options: {
          intervalMinSeconds: 30,
          intervalMaxSeconds: 60,
        },
      },
      ecom_fansclub_participate: {
        enable: false,
        messages: [],
        options: {
          intervalMinSeconds: 30,
          intervalMaxSeconds: 60,
        },
      },
    },
    blockList: [],
    ws: {
      enable: false,
      port: 12354,
    },
  }
}

const createDefaultContext = (): AutoReplyContext => ({
  isRunning: false,
  isListening: 'stopped',
  replies: [],
  comments: [],
  config: createDefaultConfig(),
})

const USERNAME_PLACEHOLDER = '{用户名}'

export const useAutoReplyStore = create<AutoReplyState & AutoReplyAction>()(
  persist(
    immer(set => {
      eventEmitter.on(EVENTS.ACCOUNT_REMOVED, (accountId: string) => {
        set(state => {
          delete state.contexts[accountId]
        })
      })

      // 迁移之前版本设置的 prompt
      const previousPrompt = localStorage.getItem('autoReplyPrompt')
      if (previousPrompt) {
        localStorage.removeItem('autoReplyPrompt')
      }

      const ensureContext = (state: AutoReplyState, accountId: string) => {
        if (!state.contexts[accountId]) {
          state.contexts[accountId] = createDefaultContext()
        }
        return state.contexts[accountId]
      }

      return {
        contexts: {},
        setIsRunning: (accountId, isRunning) =>
          set(state => {
            const context = ensureContext(state, accountId)
            context.isRunning = isRunning
          }),
        setIsListening: (accountId, isListening) =>
          set(state => {
            const context = ensureContext(state, accountId)
            context.isListening = isListening
          }),

        addComment: (accountId, comment) =>
          set(state => {
            const context = ensureContext(state, accountId)
            // 限制评论数量，防止内存无限增长
            const MAX_COMMENTS = 500
            context.comments = [{ ...comment }, ...context.comments].slice(
              0,
              MAX_COMMENTS,
            )
          }),
        addReply: (accountId, commentId, nickname, content, type = 'ai', autoSent = false) => {
          const replyId = crypto.randomUUID()
          set(state => {
            const context = ensureContext(state, accountId)
            // 限制回复数量 (可选)
            const MAX_REPLIES = 500
            context.replies = [
              {
                id: replyId,
                commentId,
                replyContent: content,
                replyFor: nickname,
                time: new Date().toISOString(),
                type,
                status: (autoSent ? 'sending' : 'pending') as ReplyPreview['status'],
                autoSent,
              },
              ...context.replies,
            ].slice(0, MAX_REPLIES)
          })
          return replyId
        },
        updateReplyStatus: (accountId, replyId, status, error) =>
          set(state => {
            const context = ensureContext(state, accountId)
            const reply = context.replies.find(r => r.id === replyId)
            if (reply) {
              reply.status = status
              if (error) {
                reply.error = error
              }
            }
          }),
        removeReply: (accountId, commentId) =>
          set(state => {
            const context = ensureContext(state, accountId)
            context.replies = context.replies.filter(
              reply => reply.commentId !== commentId,
            )
          }),
        clearComments: (accountId) =>
          set(state => {
            const context = ensureContext(state, accountId)
            context.comments = []
          }),
        clearReplies: (accountId) =>
          set(state => {
            const context = ensureContext(state, accountId)
            context.replies = []
          }),

        updateConfig: (accountId, configUpdates) =>
          set(state => {
            console.log('updateConfig 被调用:', { accountId, configUpdates })
            const context = ensureContext(state, accountId)
            console.log('更新前的配置:', context.config.comment.keywordReply.rules)
            const newConfig = mergeWithoutArray(context.config, configUpdates)
            console.log('合并后的配置:', newConfig.comment.keywordReply.rules)
            context.config = newConfig
            console.log('设置后的配置:', context.config.comment.keywordReply.rules)
          }),


      }
    }),
    {
      name: 'auto-reply',
      version: 3,
      storage: createJSONStorage(() => localStorage),
      partialize: state => {
        return {
          contexts: Object.fromEntries(
            Object.entries(state.contexts).map(([accountId, context]) => [
              accountId,
              {
                config: context.config,
                // prompt: context.prompt,
                // autoSend: context.autoSend,
                // userBlocklist: context.userBlocklist,
              },
            ]),
          ),
        }
      },
      merge: (persistedState, currentState) => {
        // 合并时，用默认值填充缺失的字段
        const mergedContexts: Record<string, AutoReplyContext> = {}
        const persistedContexts =
          (persistedState as Partial<AutoReplyState>)?.contexts || {}

        // 获取当前所有账户 ID (包括可能只在内存中的)
        const allAccountIds = new Set([
          ...Object.keys(currentState.contexts),
          ...Object.keys(persistedContexts),
        ])

        for (const accountId of allAccountIds) {
          const currentContextPartial = currentState.contexts[accountId] || {}
          const persistedContextPartial = persistedContexts[accountId] as
            | Partial<AutoReplyContext>
            | undefined

          const defaultContext = createDefaultContext()
          mergedContexts[accountId] = {
            ...defaultContext,
            ...currentContextPartial,
            ...(persistedContextPartial && {
              config: mergeWithoutArray(defaultContext.config, persistedContextPartial.config || {}),
            }),
          }
        }

        return {
          ...currentState,
          contexts: mergedContexts,
        }
      },
      migrate: (persistedState, version) => {
        if (version === 1) {
          try {
            const persisted = persistedState as {
              contexts: Record<string, { prompt: string }>
            }
            const contexts: Record<string, AutoReplyContext> = {}
            for (const key in persisted.contexts) {
              contexts[key] = createDefaultContext()
              contexts[key].config.comment.aiReply.prompt =
                persisted.contexts[key].prompt
            }

            return { contexts }
          } catch {
            return {
              contexts: {
                default: createDefaultContext(),
              },
            }
          }
        }

        if (version === 2) {
          try {
            const persisted = persistedState as {
              contexts: Record<string, AutoReplyContext>
            }

            // 迁移现有的 context 到 contextByScriptList
            for (const accountId in persisted.contexts) {
              const context = persisted.contexts[accountId]
              if (context?.config?.comment?.aiReply) {
                const aiReply = context.config.comment.aiReply

                // 如果存在旧的 context 但没有 contextByScriptList，则进行迁移
                if (aiReply.context && !aiReply.contextByScriptList) {
                  aiReply.contextByScriptList = {
                    'default': aiReply.context
                  }
                }

                // 确保 contextByScriptList 存在
                if (!aiReply.contextByScriptList) {
                  aiReply.contextByScriptList = {}
                }
              }
            }

            return persistedState
          } catch (error) {
            console.warn('数据迁移失败，使用默认配置:', error)
            return {
              contexts: {
                default: createDefaultContext(),
              },
            }
          }
        }

        return persistedState
      },
    },
  ),
)


function sendConfiguredReply(
  config: AutoReplyConfig,
  sourceMessage: Message,
): void {
  const replyConfig = config[sourceMessage.msg_type as EventMessageType]
  if (replyConfig.enable && replyConfig.messages.length > 0) {
    const filterMessages = []
    const pureMessages = []
    for (const message of replyConfig.messages) {
      if (typeof message === 'string') {
        pureMessages.push(message)
      } else if (matchObject(sourceMessage, message.filter)) {
        filterMessages.push(message.content)
      }
    }
    const replyMessages = filterMessages.length ? filterMessages : pureMessages
    const content = getRandomElement(replyMessages)
    if (content) {
      const message = replaceUsername(
        content,
        sourceMessage.nick_name,
        config.hideUsername,
      )
      sendMessage(message) // 注意：这里是异步的，但我们不等待它完成
    }
  }
}

function sendPassiveInteractionVoice(
  config: AutoReplyConfig,
  sourceMessage: Message,
): void {
  console.log('🎤 被动互动检查:', {
    messageType: sourceMessage.msg_type,
    hasPassiveConfig: !!config.passiveInteraction,
    passiveConfig: config.passiveInteraction?.[sourceMessage.msg_type as EventMessageType]
  })

  // 添加LogDisplayer日志
  window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
    scope: '被动互动',
    level: 'info',
    message: `被动互动检查: ${sourceMessage.msg_type}, 配置存在: ${!!config.passiveInteraction}`
  })

  const passiveConfig = config.passiveInteraction?.[sourceMessage.msg_type as EventMessageType]
  if (passiveConfig?.enable && passiveConfig.messages.length > 0) {
    // 检查间隔时间范围
    const minInterval = (passiveConfig.options?.intervalMinSeconds as number) || 30
    const maxInterval = (passiveConfig.options?.intervalMaxSeconds as number) || 60
    const currentTime = Date.now()
    const nextAllowedTime = nextAllowedTimes[sourceMessage.msg_type] || 0

    if (minInterval > 0 && currentTime < nextAllowedTime) {
      const remainingMs = nextAllowedTime - currentTime

      console.log('🎤 被动互动跳过（间隔时间未到）:', {
        messageType: sourceMessage.msg_type,
        minInterval,
        maxInterval,
        timeSinceLastInteraction: Math.floor((currentTime - (lastInteractionTimes[sourceMessage.msg_type] || 0)) / 1000),
        remainingTime: Math.floor(remainingMs / 1000)
      })

      window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
        scope: '被动互动',
        level: 'info',
        message: `被动互动跳过: ${sourceMessage.msg_type}, 间隔时间未到 (${minInterval}-${maxInterval}秒范围, 剩余${Math.floor(remainingMs / 1000)}秒)`
      })
      return
    }

    // 更新最后触发时间
    lastInteractionTimes[sourceMessage.msg_type] = currentTime

    // 生成下次允许触发的时间（随机间隔）
    const randomIntervalMs = (minInterval + Math.random() * (maxInterval - minInterval)) * 1000
    nextAllowedTimes[sourceMessage.msg_type] = currentTime + randomIntervalMs
    console.log('🎤 被动互动触发:', {
      messageType: sourceMessage.msg_type,
      enabled: passiveConfig.enable,
      messagesCount: passiveConfig.messages.length,
      messages: passiveConfig.messages
    })

    window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
      scope: '被动互动',
      level: 'info',
      message: `被动互动触发: ${sourceMessage.msg_type}, 消息数量: ${passiveConfig.messages.length}`
    })

    const filterMessages = []
    const pureMessages = []
    for (const message of passiveConfig.messages) {
      if (typeof message === 'string') {
        pureMessages.push(message)
      } else if (matchObject(sourceMessage, message.filter)) {
        filterMessages.push(message.content)
      }
    }
    const replyMessages = filterMessages.length ? filterMessages : pureMessages
    const content = getRandomElement(replyMessages)
    if (content) {
      const voiceText = replaceUsername(
        content,
        sourceMessage.nick_name,
        config.hideUsername,
      )

      // 发送到语音输出列表
      console.log('🎤 发送被动互动语音到IPC:', voiceText)

      const voiceData = {
        id: crypto.randomUUID(),
        text: voiceText,
        type: 'passive-interaction',
        messageType: sourceMessage.msg_type,
        timestamp: Date.now(),
      }

      window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
        scope: '被动互动',
        level: 'info',
        message: `发送被动互动语音: ${voiceText}`
      })

      // 使用invoke而不是send，确保数据能正确传递
      window.ipcRenderer.invoke(IPC_CHANNELS.autoVoice.addPassiveInteraction, voiceData)
        .then(() => {
          console.log('🎤 被动互动语音发送成功')
          window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
            scope: '被动互动',
            level: 'success',
            message: `被动互动语音发送成功: ${voiceData.id}`
          })
        })
        .catch((error) => {
          console.error('🎤 被动互动语音发送失败:', error)
          window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
            scope: '被动互动',
            level: 'error',
            message: `被动互动语音发送失败: ${error}`
          })
        })
    } else {
      window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
        scope: '被动互动',
        level: 'warn',
        message: `被动互动跳过: ${sourceMessage.msg_type} 没有可用的消息内容`
      })
    }
  } else {
    window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
      scope: '被动互动',
      level: 'warn',
      message: `被动互动跳过: ${sourceMessage.msg_type} 未启用或无消息配置`
    })
  }
}

// 记录每种消息类型的最后触发时间和下次允许触发的时间
const lastInteractionTimes: Record<string, number> = {}
const nextAllowedTimes: Record<string, number> = {}

function getRandomElement<T>(arr: T[]): T | undefined {
  if (arr.length === 0) return undefined
  const randomIndex = Math.floor(Math.random() * arr.length)
  return arr[randomIndex]
}

async function sendMessage(content: string, replyId?: string, updateStatus?: (replyId: string, status: ReplyPreview['status'], error?: string) => void, username?: string, mask?: boolean) {
  if (!content) return { success: false, error: '内容为空' }

  try {
    if (replyId && updateStatus) {
      updateStatus(replyId, 'sending')
    }

    // 发送到直播间时需要添加@用户名前缀
    const messageToSend = username ? formatReplyContent(content, username, mask || false, false, true) : content

    await window.ipcRenderer.invoke(
      IPC_CHANNELS.tasks.autoReply.sendReply,
      messageToSend,
    )

    if (replyId && updateStatus) {
      updateStatus(replyId, 'sent')
    }

    return { success: true }
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : String(err)
    console.error('自动发送回复失败:', errorMessage)

    if (replyId && updateStatus) {
      updateStatus(replyId, 'failed', errorMessage)
    }

    return { success: false, error: errorMessage }
  }
}

function replaceUsername(content: string, username: string, mask: boolean) {
  if (!content) return ''
  // 把 {用户名} 替换为 username
  const displayedUsername = mask
    ? `${String.fromCodePoint(username.codePointAt(0) ?? 42 /* 42 是星号 */)}***`
    : username
  return content.replace(
    new RegExp(USERNAME_PLACEHOLDER, 'g'),
    displayedUsername,
  )
}

/**
 * 格式化回复内容
 * @param content 原始回复内容
 * @param username 用户昵称
 * @param mask 是否隐藏用户名
 * @param forVoice 是否用于语音输出（语音不需要@）
 * @param forSending 是否用于发送到直播间（发送时需要@，显示时不需要）
 * @returns 格式化后的内容
 */
function formatReplyContent(content: string, username: string, mask: boolean, forVoice: boolean = false, forSending: boolean = false) {
  if (!content) return ''

  // 先替换 {用户名} 占位符
  const processedContent = replaceUsername(content, username, mask)

  if (forVoice) {
    // 语音输出不需要@用户名
    return processedContent
  } else if (forSending) {
    // 发送到直播间时需要添加@用户名格式
    const displayedUsername = mask
      ? `${String.fromCodePoint(username.codePointAt(0) ?? 42 /* 42 是星号 */)}***`
      : username
    return `@${displayedUsername} ${processedContent}`
  } else {
    // 显示在回复列表时不需要@用户名
    return processedContent
  }
}

export function useAutoReply() {
  const store = useAutoReplyStore()
  const { currentAccountId } = useAccounts()
  const accountName = useCurrentLiveControl(ctx => ctx.accountName)
  const aiStore = useAIChatStore()

  // 获取当前话术列表ID，用于获取对应的知识库信息
  const getCurrentScriptListId = () => {
    try {
      // 动态导入 useAutoVoice 以避免循环依赖
      const { useAutoVoice } = require('./useAutoVoice')
      const autoVoiceStore = useAutoVoice.getState()
      return autoVoiceStore.currentScriptListId
    } catch (error) {
      console.warn('无法获取当前话术列表ID:', error)
      return 'default'
    }
  }

  // 直接从store获取当前账户的上下文，确保响应式更新
  const context = store.contexts[currentAccountId] || createDefaultContext()
  const { isRunning, isListening, comments, replies, config } = context

  /**
   * 处理关键字回复逻辑
   * @returns boolean - 是否成功匹配并发送了关键字回复
   */
  const handleKeywordReply = useMemoizedFn(
    (comment: MessageOf<'comment'>, config: AutoReplyConfig, accountId: string): boolean => {
      if (!config.comment.keywordReply.enable || !comment.content) {
        return false
      }

      const rule = config.comment.keywordReply.rules.find(({ keywords }) =>
        keywords.some(kw => comment.content?.includes(kw)),
      )

      if (rule && rule.contents.length > 0) {
        const content = getRandomElement(rule.contents)
        if (content) {
          // 格式化文字回复（显示时不带@用户名）
          const textReply = formatReplyContent(
            content,
            comment.nick_name,
            config.hideUsername,
            false, // 不是语音回复
            false  // 显示时不需要@
          )

          // 格式化语音回复（不带@用户名）
          const voiceReply = formatReplyContent(
            content,
            comment.nick_name,
            config.hideUsername,
            true, // 语音回复不需要@
            false // 不是发送
          )

          // 根据autoSend配置决定是否自动发送
          const autoSend = config.comment.keywordReply.autoSend

          // 添加关键词回复到回复列表
          const replyId = store.addReply(
            accountId,
            comment.msg_id,
            comment.nick_name,
            textReply,
            'keyword',
            autoSend // 根据配置决定是否自动发送
          )

          if (autoSend) {
            // 发送文字消息并更新状态
            sendMessage(textReply, replyId, (id, status, error) => {
              store.updateReplyStatus(accountId, id, status, error)
              // 记录回复日志
              if (status === 'sent') {
                autoReplyLogger.success(`关键词回复发送成功: ${textReply}`)
              } else if (status === 'failed') {
                autoReplyLogger.error(`关键词回复发送失败: ${error}`)
              }
            }, comment.nick_name, config.hideUsername)
          }

          // 根据autoSendToVoice配置决定是否添加到语音输出列表
          if (config.comment.keywordReply.autoSendToVoice) {
            const voiceData = {
              id: crypto.randomUUID(),
              text: voiceReply,
              type: 'comment-reply',
              replyType: 'keyword',
              timestamp: Date.now(),
            }

            window.ipcRenderer.invoke(IPC_CHANNELS.autoVoice.addCommentReply, voiceData)
              .then(() => {
                console.log('💬 关键词回复语音发送成功')
                autoReplyLogger.success(`关键词回复语音已添加: ${voiceReply}`)
              })
              .catch((error) => {
                console.error('💬 关键词回复语音发送失败:', error)
                autoReplyLogger.error(`关键词回复语音发送失败: ${error}`)
              })
          }

          autoReplyLogger.info(`匹配关键词回复: ${rule.keywords.join(', ')} -> ${textReply}`)
          return true // 匹配成功
        }
      }
      return false // 未匹配
    },
  )

  /**
   * 处理 AI 回复逻辑
   */
  const handleAIReply = useMemoizedFn(
    async (
      accountId: string,
      comment: MessageOf<'comment'>,
      config: AutoReplyConfig,
    ) => {
      // 检查助理开关和AI回复开关
      if (!config.assistantEnabled || !config.comment.aiReply.enable) return

      const { prompt, context, contextByScriptList, autoSend } = config.comment.aiReply
      const { provider, model } = aiStore.config
      const apiKey = aiStore.apiKeys[provider]
      const customBaseURL = aiStore.customBaseURL

      // 创建独立的AI请求消息（只包含当前评论内容）
      const plainMessages = [
        {
          role: 'user' as const,
          content: comment.content
        }
      ]

      // 获取当前话术列表对应的知识库信息
      const currentScriptListId = getCurrentScriptListId()
      const currentContext = contextByScriptList[currentScriptListId] || context

      // 构造系统提示词（不包含评论上下文）
      const systemPrompt = `${prompt}

${currentContext}

你将接收到用户的评论内容。请根据以上信息回复用户评论。

重要：请直接返回回复内容，不要包含用户昵称或@符号，系统会自动处理昵称格式。如果评论内容不适合回复（如无意义内容、广告、恶意评论等），请直接返回 false。`

      const messages = [
        { role: 'system', content: systemPrompt }, // id 和 timestamp 对请求不重要
        ...plainMessages,
      ]

      try {
        const replyContent = await window.ipcRenderer.invoke(
          IPC_CHANNELS.tasks.aiChat.normalChat,
          {
            messages,
            provider,
            model,
            apiKey,
            customBaseURL,
          },
        )

        // 检查AI是否返回false表示跳过回复
        if (replyContent === 'false') {
          console.log('AI决定跳过回复:', comment.content)
          return
        }

        if (replyContent && typeof replyContent === 'string' && replyContent.trim()) {
          const rawContent = replyContent.trim()

          // 解析AI返回的多个回复，格式为 <回复内容>
          const replyMatches = rawContent.match(/<([^>]+)>/g)

          if (replyMatches && replyMatches.length > 0) {
            // 提取第一个回复内容（对应当前评论）
            const firstReply = replyMatches[0].replace(/^<|>$/g, '').trim()

            if (firstReply) {
              // 格式化文字回复（显示时不带@用户名）
              const textReply = formatReplyContent(
                firstReply,
                comment.nick_name,
                config.hideUsername,
                false, // 不是语音回复
                false  // 显示时不需要@
              )

              // 格式化语音回复（不带@用户名）
              const voiceReply = formatReplyContent(
                firstReply,
                comment.nick_name,
                config.hideUsername,
                true, // 语音回复不需要@
                false // 不是发送
              )

              // 将 AI 回复添加到状态中
              const replyId = store.addReply(
                accountId,
                comment.msg_id,
                comment.nick_name,
                textReply,
                'ai',
                autoSend
              )

              autoReplyLogger.success(`AI回复生成成功: ${textReply}`)

              // 如果开启自动发送，则发送文字消息
              if (autoSend) {
                sendMessage(textReply, replyId, (id, status, error) => {
                  store.updateReplyStatus(accountId, id, status, error)
                  // 记录回复日志
                  if (status === 'sent') {
                    autoReplyLogger.success(`AI回复发送成功: ${textReply}`)
                  } else if (status === 'failed') {
                    autoReplyLogger.error(`AI回复发送失败: ${error}`)
                  }
                }, comment.nick_name, config.hideUsername)
              } else {
                autoReplyLogger.info(`AI回复已生成，等待手动发送: ${textReply}`)
              }

              // 根据autoSendToVoice配置决定是否添加到语音输出列表
              if (config.comment.aiReply.autoSendToVoice) {
                const voiceData = {
                  id: crypto.randomUUID(),
                  text: voiceReply,
                  type: 'comment-reply',
                  replyType: 'ai',
                  timestamp: Date.now(),
                }

                window.ipcRenderer.invoke(IPC_CHANNELS.autoVoice.addCommentReply, voiceData)
                  .then(() => {
                    console.log('💬 AI回复语音发送成功')
                    autoReplyLogger.success(`AI回复语音已添加: ${voiceReply}`)
                  })
                  .catch((error) => {
                    console.error('💬 AI回复语音发送失败:', error)
                    autoReplyLogger.error(`AI回复语音发送失败: ${error}`)
                  })
              }
            } else {
              autoReplyLogger.error('AI回复解析失败：提取的回复内容为空')
            }
          } else {
            // 如果没有找到 <> 格式，尝试使用原始内容
            const originalContent = rawContent

            // 格式化文字回复（显示时不带@用户名）
            const textReply = formatReplyContent(
              originalContent,
              comment.nick_name,
              config.hideUsername,
              false, // 不是语音回复
              false  // 显示时不需要@
            )

            // 格式化语音回复（不带@用户名）
            const voiceReply = formatReplyContent(
              originalContent,
              comment.nick_name,
              config.hideUsername,
              true, // 语音回复不需要@
              false // 不是发送
            )

            // 将 AI 回复添加到状态中
            const replyId = store.addReply(
              accountId,
              comment.msg_id,
              comment.nick_name,
              textReply,
              'ai',
              autoSend
            )

            autoReplyLogger.success(`AI回复生成成功: ${textReply}`)

            // 如果开启自动发送，则发送文字消息
            if (autoSend) {
              sendMessage(textReply, replyId, (id, status, error) => {
                store.updateReplyStatus(accountId, id, status, error)
                // 记录回复日志
                if (status === 'sent') {
                  autoReplyLogger.success(`AI回复发送成功: ${textReply}`)
                } else if (status === 'failed') {
                  autoReplyLogger.error(`AI回复发送失败: ${error}`)
                }
              }, comment.nick_name, config.hideUsername)
            } else {
              autoReplyLogger.info(`AI回复已生成，等待手动发送: ${textReply}`)
            }

            // 根据autoSendToVoice配置决定是否添加到语音输出列表
            if (config.comment.aiReply.autoSendToVoice) {
              const voiceData = {
                id: crypto.randomUUID(),
                text: voiceReply,
                type: 'comment-reply',
                replyType: 'ai',
                timestamp: Date.now(),
              }

              window.ipcRenderer.invoke(IPC_CHANNELS.autoVoice.addCommentReply, voiceData)
                .then(() => {
                  console.log('💬 AI回复语音发送成功')
                  autoReplyLogger.success(`AI回复语音已添加: ${voiceReply}`)
                })
                .catch((error) => {
                  console.error('💬 AI回复语音发送失败:', error)
                  autoReplyLogger.error(`AI回复语音发送失败: ${error}`)
                })
            }
          }
        } else {
          autoReplyLogger.error('AI回复为空或格式错误')
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err)
        console.error('AI 生成回复失败:', errorMessage)
        autoReplyLogger.error(`AI回复生成失败: ${errorMessage}`)
        // 可以在这里添加错误处理，比如更新状态或提示用户
      }
    },
  )

  const handleComment = useMemoizedFn((comment: Message, accountId: string, isManual: boolean = false) => {
    // const context = contexts[accountId] || createDefaultContext()
    const currentContext =
      useAutoReplyStore.getState().contexts[accountId] || createDefaultContext()
    const {
      isRunning,
      config,
    } = currentContext

    store.addComment(accountId, comment)

    // 记录评论日志
    if (comment.msg_type === 'comment') {
      autoReplyLogger.info(`收到评论: ${comment.nick_name} - ${comment.content}${isManual ? ' (手动添加)' : ''}`)
    }

    if (
      !isRunning ||
      // 如果是主播评论就跳过（但手动添加的评论不跳过，用于测试）
      (!isManual && comment.nick_name === accountName) ||
      // 在黑名单也跳过
      config.blockList?.includes(comment.nick_name)
    ) {
      if (!isRunning) {
        autoReplyLogger.info(`跳过处理: 自动回复未启动`)
      } else if (!isManual && comment.nick_name === accountName) {
        autoReplyLogger.info(`跳过处理: 主播自己的评论`)
      } else if (config.blockList?.includes(comment.nick_name)) {
        autoReplyLogger.info(`跳过处理: 用户在黑名单中 - ${comment.nick_name}`)
      }
      return
    }

    switch (comment.msg_type) {
      case 'comment': {
        // 优先尝试关键字回复
        const keywordReplied = handleKeywordReply(comment, config, accountId)
        // 如果关键字未回复，且助理和AI回复都已启用，则尝试 AI 回复
        if (!keywordReplied && config.assistantEnabled && config.comment.aiReply.enable) {
          handleAIReply(accountId, comment, config)
        }
        break
      }
      case 'live_order': {
        /* 如果设置了仅已支付回复且当前非已支付时不回复 */
        if (
          !config.live_order.options?.onlyReplyPaid ||
          comment.order_status === '已付款'
        ) {
          sendConfiguredReply(config, comment)
        }

        // 处理被动互动语音（下单事件）
        if (
          !config.passiveInteraction?.live_order?.options?.onlyReplyPaid ||
          comment.order_status === '已付款'
        ) {
          sendPassiveInteractionVoice(config, comment)
        }
        break
      }
      default:
        // 处理其他类型的自动回复
        sendConfiguredReply(config, comment)
        // 处理被动互动语音
        sendPassiveInteractionVoice(config, comment)
    }
  })

  return {
    // 当前账户的状态
    isRunning,
    isListening,
    comments, // 当前账户的评论
    replies, // 当前账户的回复
    config, // 当前账户的配置

    // Actions (绑定到当前账户)
    handleComment,
    setIsRunning: (running: boolean) =>
      store.setIsRunning(currentAccountId, running),
    setIsListening: (listening: ListeningStatus) =>
      store.setIsListening(currentAccountId, listening),
    removeReply: (commentId: string) =>
      store.removeReply(currentAccountId, commentId),
    updateReplyStatus: (replyId: string, status: ReplyPreview['status'], error?: string) =>
      store.updateReplyStatus(currentAccountId, replyId, status, error),
    clearComments: () => store.clearComments(currentAccountId),
    clearReplies: () => store.clearReplies(currentAccountId),
    addManualComment: (content: string, nickname: string = '游客') => {
      const comment: Message = {
        msg_type: 'comment',
        msg_id: crypto.randomUUID(),
        nick_name: nickname,
        content: content,
        time: new Date().toLocaleTimeString(),
      }
      // 使用 handleComment 来处理手动添加的评论，这样会触发关键词回复和AI回复
      // 传递 isManual=true 表示这是手动添加的评论
      handleComment(comment, currentAccountId, true)
    },

    // 快捷方式更新 prompt (示例)
    updateKeywordRules: (
      rules: AutoReplyConfig['comment']['keywordReply']['rules'],
    ) => {
      console.log('updateKeywordRules 被调用:', rules)
      console.log('当前账户ID:', currentAccountId)
      store.updateConfig(currentAccountId, {
        comment: { keywordReply: { rules } },
      })
    },
    updateAIReplySettings: (
      settings: DeepPartial<AutoReplyConfig['comment']['aiReply']>,
    ) => {
      store.updateConfig(currentAccountId, { comment: { aiReply: settings } })
    },
    resetAIReplyPromptToDefault: () => {
      store.updateConfig(currentAccountId, {
        comment: {
          aiReply: {
            prompt: defaultPrompt
          }
        }
      })
    },
    updateGeneralSettings: (
      settings: DeepPartial<Pick<AutoReplyConfig, 'entry' | 'hideUsername' | 'hideHost'>>,
    ) => {
      store.updateConfig(currentAccountId, settings)
    },
    updateEventReplyContents: (
      replyType: EventMessageType,
      contents: SimpleEventReplyMessage[],
    ) => {
      store.updateConfig(currentAccountId, {
        [replyType]: { messages: contents },
      })
    },
    updateBlockList: (blockList: string[]) => {
      store.updateConfig(currentAccountId, { blockList })
    },
    updateKeywordReplyEnabled: (enable: boolean) => {
      store.updateConfig(currentAccountId, {
        comment: { keywordReply: { enable } },
      })
    },
    updateKeywordReplySettings: (
      settings: DeepPartial<AutoReplyConfig['comment']['keywordReply']>,
    ) => {
      store.updateConfig(currentAccountId, { comment: { keywordReply: settings } })
    },
    updateEventReplyEnabled: (replyType: EventMessageType, enable: boolean) => {
      store.updateConfig(currentAccountId, {
        [replyType]: { enable },
      })
    },
    updateEventReplyOptions: <T extends EventMessageType>(
      replyType: T,
      options: AutoReplyConfig[T]['options'],
    ) => {
      store.updateConfig(currentAccountId, {
        [replyType]: { options },
      })
    },
    updateWSConfig: (wsConfig: DeepPartial<AutoReplyConfig['ws']>) => {
      store.updateConfig(currentAccountId, { ws: wsConfig })
    },
    updateLiveMode: (liveMode: 'voice' | 'digital-human') => {
      store.updateConfig(currentAccountId, { liveMode })
    },
    updateAssistantEnabled: (enabled: boolean) => {
      store.updateConfig(currentAccountId, { assistantEnabled: enabled })
    },
    updateAssistantMode: (mode: 'voice' | 'digital-human') => {
      console.log('🔄 updateAssistantMode 被调用:', {
        mode,
        currentAccountId,
        timestamp: new Date().toLocaleTimeString()
      })
      store.updateConfig(currentAccountId, { assistantMode: mode })
      console.log('🔄 updateConfig 调用完成')
    },

    // 被动互动相关方法
    updatePassiveInteractionContents: (
      replyType: EventMessageType,
      contents: SimpleEventReplyMessage[],
    ) => {
      store.updateConfig(currentAccountId, {
        passiveInteraction: { [replyType]: { messages: contents } },
      })
    },
    updatePassiveInteractionEnabled: (replyType: EventMessageType, enable: boolean) => {
      store.updateConfig(currentAccountId, {
        passiveInteraction: { [replyType]: { enable } },
      })
    },
    updatePassiveInteractionOptions: <T extends EventMessageType>(
      replyType: T,
      options: AutoReplyConfig['passiveInteraction'][T]['options'],
    ) => {
      store.updateConfig(currentAccountId, {
        passiveInteraction: { [replyType]: { options } },
      })
    },

    // 更新特定话术列表的知识库信息
    updateContextByScriptList: (scriptListId: string, context: string) => {
      store.updateConfig(currentAccountId, {
        comment: {
          aiReply: {
            contextByScriptList: {
              [scriptListId]: context
            }
          }
        }
      })
    },

    // 获取当前话术列表的知识库信息
    getCurrentContext: () => {
      const currentScriptListId = getCurrentScriptListId()
      return config.comment.aiReply.contextByScriptList[currentScriptListId] || config.comment.aiReply.context
    },

    // 可以根据需要添加更多快捷更新配置的方法
  }
}
