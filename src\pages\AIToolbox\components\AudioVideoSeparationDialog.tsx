import React, { useState, useCallback, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/useToast'
import { Upload, FileVideo, Music, Download } from 'lucide-react'
import { cn } from '@/lib/utils'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { useComfyUIBasePath } from '@/hooks/useVoiceSettings'

interface AudioVideoSeparationDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
}

interface VideoFile {
    file: File
    name: string
    size: number
    duration?: number
}

interface SeparationOptions {
    audioFormat: 'mp3' | 'wav' | 'aac'
    extractAudio: boolean
    extractVideo: boolean
}

interface VideoInfo {
    duration?: number
    filename?: string
    error?: string
}

export default function AudioVideoSeparationDialog({ open, onOpenChange }: AudioVideoSeparationDialogProps) {
    const [videoFile, setVideoFile] = useState<VideoFile | null>(null)
    const [separationOptions, setSeparationOptions] = useState<SeparationOptions>({ 
        audioFormat: 'mp3', 
        extractAudio: true, 
        extractVideo: true 
    })
    const [isProcessing, setIsProcessing] = useState(false)
    const [progress, setProgress] = useState(0)
    const [isDragOver, setIsDragOver] = useState(false)
    const [results, setResults] = useState<{
        audioPath?: string
        videoPath?: string
        audioFilename?: string
        videoFilename?: string
    }>({})
    const fileInputRef = useRef<HTMLInputElement>(null)
    const { toast } = useToast()
    const comfyUIBasePath = useComfyUIBasePath()

    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(true)
    }, [])

    const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(false)
    }, [])

    const handleDrop = useCallback(async (e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(false)

        const files = Array.from(e.dataTransfer.files)
        const videoFile = files.find(file => file.type.startsWith('video/'))

        if (videoFile) {
            const newVideoFile = {
                file: videoFile,
                name: videoFile.name,
                size: videoFile.size
            }
            setVideoFile(newVideoFile)

            // 获取视频时长
            try {
                const arrayBuffer = await videoFile.arrayBuffer()
                const uint8Array = new Uint8Array(arrayBuffer)
                const tempFileName = `temp_info_${Date.now()}_${videoFile.name}`
                await window.ipcRenderer.invoke(IPC_CHANNELS.video.writeFile as any, tempFileName, uint8Array)

                const videoInfo = await window.ipcRenderer.invoke(IPC_CHANNELS.video.getInfo as any, tempFileName) as VideoInfo
                if (!videoInfo.error && videoInfo.duration) {
                    setVideoFile(prev => prev ? { ...prev, duration: videoInfo.duration } : null)
                }
            } catch (error) {
                console.error('获取视频信息失败:', error)
            }
        } else {
            toast.error('请拖入视频文件')
        }
    }, [toast])

    const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]
        if (file && file.type.startsWith('video/')) {
            const newVideoFile = {
                file,
                name: file.name,
                size: file.size
            }
            setVideoFile(newVideoFile)
        }
    }, [])

    const handleSeparation = useCallback(async () => {
        if (!videoFile || !comfyUIBasePath) {
            toast.error('请选择视频文件并确保ComfyUI路径已设置')
            return
        }

        if (!separationOptions.extractAudio && !separationOptions.extractVideo) {
            toast.error('请至少选择提取音频或视频')
            return
        }

        setIsProcessing(true)
        setProgress(0)
        setResults({})

        try {
            // 设置ComfyUI路径
            await window.ipcRenderer.invoke(IPC_CHANNELS.video.setComfyUIPath as any, comfyUIBasePath)

            // 上传文件
            const arrayBuffer = await videoFile.file.arrayBuffer()
            const uint8Array = new Uint8Array(arrayBuffer)
            const tempFileName = `temp_separation_${Date.now()}_${videoFile.name}`
            
            setProgress(10)
            await window.ipcRenderer.invoke(IPC_CHANNELS.video.writeFile as any, tempFileName, uint8Array)

            const newResults: typeof results = {}

            // 分离音频
            if (separationOptions.extractAudio) {
                setProgress(30)
                const audioFilename = `audio_${Date.now()}.${separationOptions.audioFormat}`
                const audioResult = await window.ipcRenderer.invoke(IPC_CHANNELS.video.separateAudio as any, {
                    originalFilename: tempFileName,
                    outputAudioFilename: audioFilename,
                    audioFormat: separationOptions.audioFormat
                })

                if (audioResult.success) {
                    newResults.audioPath = audioResult.outputAudioPath
                    newResults.audioFilename = audioResult.outputAudioFilename
                } else {
                    throw new Error(`音频分离失败: ${audioResult.error}`)
                }
            }

            // 提取视频
            if (separationOptions.extractVideo) {
                setProgress(60)
                const videoFilename = `video_${Date.now()}.mp4`
                const videoResult = await window.ipcRenderer.invoke(IPC_CHANNELS.video.extractVideo as any, {
                    originalFilename: tempFileName,
                    outputVideoFilename: videoFilename
                })

                if (videoResult.success) {
                    newResults.videoPath = videoResult.outputVideoPath
                    newResults.videoFilename = videoResult.outputVideoFilename
                } else {
                    throw new Error(`视频提取失败: ${videoResult.error}`)
                }
            }

            setProgress(100)
            setResults(newResults)

            toast.success('文件已保存到输出目录')

        } catch (error) {
            console.error('音画分离失败:', error)
            toast.error(error instanceof Error ? error.message : '未知错误')
        } finally {
            setIsProcessing(false)
        }
    }, [videoFile, separationOptions, comfyUIBasePath, toast])

    const handleShowInFolder = useCallback(async (filePath: string) => {
        try {
            await window.ipcRenderer.invoke(IPC_CHANNELS.shell.showItemInFolder as any, filePath)
        } catch (error) {
            console.error('打开文件夹失败:', error)
            toast.error('无法在文件管理器中显示文件')
        }
    }, [toast])

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const formatDuration = (seconds: number) => {
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        const secs = Math.floor(seconds % 60)
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>音画分离工具</DialogTitle>
                    <DialogDescription>
                        拖入视频文件，分离出音频和视频并保存下载
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                    {/* 文件上传区域 */}
                    <div className="space-y-4">
                        <Label>选择视频文件</Label>
                        <Card
                            className={cn(
                                "border-2 border-dashed transition-colors cursor-pointer",
                                isDragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25",
                                videoFile && "border-primary bg-primary/5"
                            )}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                            onClick={() => fileInputRef.current?.click()}
                        >
                            <CardContent className="flex flex-col items-center justify-center p-8 text-center">
                                {videoFile ? (
                                    <div className="space-y-2">
                                        <FileVideo className="w-12 h-12 text-primary mx-auto" />
                                        <div className="space-y-1">
                                            <p className="font-medium">{videoFile.name}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {formatFileSize(videoFile.size)}
                                                {videoFile.duration && ` • ${formatDuration(videoFile.duration)}`}
                                            </p>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="space-y-2">
                                        <Upload className="w-12 h-12 text-muted-foreground mx-auto" />
                                        <div className="space-y-1">
                                            <p className="font-medium">拖拽视频文件到此处</p>
                                            <p className="text-sm text-muted-foreground">
                                                或点击选择文件
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                        <input
                            ref={fileInputRef}
                            type="file"
                            accept="video/*"
                            onChange={handleFileSelect}
                            className="hidden"
                        />
                    </div>

                    {/* 分离选项 */}
                    <div className="space-y-4">
                        <Label>分离选项</Label>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="extractAudio"
                                    checked={separationOptions.extractAudio}
                                    onChange={(e) => setSeparationOptions(prev => ({ ...prev, extractAudio: e.target.checked }))}
                                />
                                <label htmlFor="extractAudio" className="text-sm font-medium">
                                    提取音频
                                </label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="extractVideo"
                                    checked={separationOptions.extractVideo}
                                    onChange={(e) => setSeparationOptions(prev => ({ ...prev, extractVideo: e.target.checked }))}
                                />
                                <label htmlFor="extractVideo" className="text-sm font-medium">
                                    提取视频（无音频）
                                </label>
                            </div>
                        </div>

                        {separationOptions.extractAudio && (
                            <div className="space-y-2">
                                <Label>音频格式</Label>
                                <Select
                                    value={separationOptions.audioFormat}
                                    onValueChange={(value: 'mp3' | 'wav' | 'aac') => 
                                        setSeparationOptions(prev => ({ ...prev, audioFormat: value }))
                                    }
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="mp3">MP3</SelectItem>
                                        <SelectItem value="wav">WAV</SelectItem>
                                        <SelectItem value="aac">AAC</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        )}
                    </div>

                    {/* 进度条 */}
                    {isProcessing && (
                        <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                                <span>处理进度</span>
                                <span>{progress}%</span>
                            </div>
                            <Progress value={progress} />
                        </div>
                    )}

                    {/* 结果显示 */}
                    {(results.audioPath || results.videoPath) && (
                        <div className="space-y-4">
                            <Label>分离结果</Label>
                            <div className="space-y-2">
                                {results.audioPath && (
                                    <Card>
                                        <CardContent className="flex items-center justify-between p-4">
                                            <div className="flex items-center space-x-3">
                                                <Music className="w-5 h-5 text-primary" />
                                                <div>
                                                    <p className="font-medium">音频文件</p>
                                                    <p className="text-sm text-muted-foreground">{results.audioFilename}</p>
                                                </div>
                                            </div>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleShowInFolder(results.audioPath!)}
                                            >
                                                <Download className="w-4 h-4 mr-2" />
                                                打开文件夹
                                            </Button>
                                        </CardContent>
                                    </Card>
                                )}
                                {results.videoPath && (
                                    <Card>
                                        <CardContent className="flex items-center justify-between p-4">
                                            <div className="flex items-center space-x-3">
                                                <FileVideo className="w-5 h-5 text-primary" />
                                                <div>
                                                    <p className="font-medium">视频文件（无音频）</p>
                                                    <p className="text-sm text-muted-foreground">{results.videoFilename}</p>
                                                </div>
                                            </div>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleShowInFolder(results.videoPath!)}
                                            >
                                                <Download className="w-4 h-4 mr-2" />
                                                打开文件夹
                                            </Button>
                                        </CardContent>
                                    </Card>
                                )}
                            </div>
                        </div>
                    )}
                </div>

                <DialogFooter>
                    <Button variant="outline" onClick={() => onOpenChange(false)}>
                        关闭
                    </Button>
                    <Button 
                        onClick={handleSeparation}
                        disabled={!videoFile || isProcessing || (!separationOptions.extractAudio && !separationOptions.extractVideo)}
                    >
                        {isProcessing ? '处理中...' : '开始分离'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
