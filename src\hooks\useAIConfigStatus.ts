import { useAIChatStore } from '@/hooks/useAIChat'
import { providers } from 'shared/providers'

/**
 * 检查AI配置状态，判断是否需要显示红点提示
 * @returns {boolean} 是否需要显示红点提示
 */
export function useAIConfigStatus() {
  const { config, apiKeys } = useAIChatStore()
  
  // 检查是否有选择模型
  const hasSelectedModel = !!config.model
  
  // 检查是否有配置API Key
  const hasApiKey = !!apiKeys[config.provider]
  
  // 如果没有选择模型，或者选择了模型但没有配置API Key，则需要显示红点提示
  const shouldShowWarning = !hasSelectedModel || !hasApiKey
  
  return shouldShowWarning
}