import { Title } from '@/components/common/Title'
import { AccountSetting } from '@/pages/SettingsPage/components/AccountSetting'
import { BrowserSetting } from '@/pages/SettingsPage/components/BrowserSetting'
import { DevSetting } from '@/pages/SettingsPage/components/DevSetting'
import { OtherSetting } from '@/pages/SettingsPage/components/OtherSetting'
import { UpdateSetting } from '@/pages/SettingsPage/components/UpdateSetting'
import { ThemeSetting } from '@/pages/SettingsPage/components/ThemeSetting'
import ForbiddenWordsSettings from '@/pages/SettingsPage/components/ForbiddenWordsSettings'
import AIChatSettings from '@/pages/SettingsPage/components/AIChatSettings'
import APIKeySettings from '@/pages/SettingsPage/components/APIKeySettings'
import VoiceSettings from '@/pages/SettingsPage/components/VoiceSettings'
import DigitalHumanSettings from '@/pages/SettingsPage/components/DigitalHumanSettings'

import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useEffect } from 'react'
import { useLocation, useSearchParams } from 'react-router'
import { useTabPersistence } from '@/hooks/useTabPersistence'
import { useAIConfigStatus } from '@/hooks/useAIConfigStatus'

export default function Settings() {
  const location = useLocation()
  const [searchParams] = useSearchParams()
  const [activeTab, setActiveTab] = useTabPersistence('app-settings-tab', 'general')
  const shouldShowWarning = useAIConfigStatus()

  // 处理URL参数中的tab参数
  useEffect(() => {
    const tabParam = searchParams.get('tab')
    if (tabParam) {
      setActiveTab(tabParam)
    }
  }, [searchParams, setActiveTab])

  // 处理锚点定位（因为是路由是 Hash 模式，无法自动处理锚点定位）
  useEffect(() => {
    const hash = location.hash
    if (hash) {
      const el = document.querySelector(hash)
      if (el) {
        el.scrollIntoView({ behavior: 'smooth' })
      }
    }
  })

  return (
    <div className="container">
      <div className="mb-6">
        <Title title="应用设置" description="管理您的应用程序设置和偏好" />
      </div>

      <Card>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-7">
              <TabsTrigger value="digital-human">数字人设置</TabsTrigger>
              <TabsTrigger value="voice">音色设置</TabsTrigger>
              <TabsTrigger value="ai-chat">AI助手</TabsTrigger>
              <TabsTrigger value="api-keys">
                <div className="flex items-center gap-2">
                  <span>API密钥</span>
                  {shouldShowWarning && (
                    <div className="relative">
                      <Badge className="absolute -top-2 -right-2 w-2 h-2 p-0 bg-red-500 rounded-full" />
                    </div>
                  )}
                </div>
              </TabsTrigger>
              <TabsTrigger value="forbidden">内容安全</TabsTrigger>
              <TabsTrigger value="general">常规设置</TabsTrigger>
              <TabsTrigger value="advanced">高级设置</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-8 mt-6">
              <ThemeSetting />
              <BrowserSetting />
              <UpdateSetting />
              <AccountSetting />
            </TabsContent>

            <TabsContent value="api-keys" className="mt-6">
              <APIKeySettings />
            </TabsContent>

            <TabsContent value="forbidden" className="mt-6">
              <ForbiddenWordsSettings />
            </TabsContent>

            <TabsContent value="ai-chat" className="mt-6">
              <AIChatSettings />
            </TabsContent>

            <TabsContent value="voice" className="mt-6">
              <VoiceSettings />
            </TabsContent>

            <TabsContent value="digital-human" className="mt-6">
              <DigitalHumanSettings />
            </TabsContent>

            <TabsContent value="advanced" className="space-y-8 mt-6">
              <DevSetting />
              <OtherSetting />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
