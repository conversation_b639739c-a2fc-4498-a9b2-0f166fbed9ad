import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useToast } from '@/hooks/useToast'
import { FilterIcon, X } from 'lucide-react'
import type { EventMessageType, SimpleEventReplyMessage } from '@/hooks/useAutoReply'
import MessageFilter from './MessageFilter'
import FilterText from './FilterText'

type MessageFilterForm<T extends EventMessageType> = {
  type: keyof import('@/utils/filter').StringFilter
  field: 'nick_name'  // 限制为 nick_name 字段
  content: string[]
}

interface ReplyMessageManagerProps {
  title: string
  description?: string
  messages: SimpleEventReplyMessage[]
  onAdd: (message: SimpleEventReplyMessage) => void
  onRemove: (index: number) => void
  placeholder?: string
  msgType: EventMessageType
}

const ReplyMessageManager: React.FC<ReplyMessageManagerProps> = ({
  title,
  description,
  messages,
  onAdd,
  onRemove,
  placeholder,
  msgType,
}) => {
  const defaultFilterForm = () => ({
    type: 'eq' as const,
    field: 'nick_name' as const,
    content: [''], // 默认提供一个空的输入框
  })
  const [newMessage, setNewMessage] = useState('')
  const [filterForm, setFilterForm] = useState<
    MessageFilterForm<typeof msgType>
  >(defaultFilterForm())
  const { toast } = useToast()

  const handleAdd = () => {
    const trimedMessage = newMessage.trim()
    if (!trimedMessage) {
      toast.error('消息内容不能为空')
      return
    }
    const filterContent = filterForm.content
    if (!filterContent.filter(Boolean).length) {
      // 未设置过滤器
      onAdd(trimedMessage)
    } else {
      onAdd({
        content: trimedMessage,
        filter: {
          [filterForm.field]: {
            [filterForm.type]: filterForm.content.filter(Boolean), // 过滤掉空字符串
          },
        },
      })
    }
    setNewMessage('')
    setFilterForm(defaultFilterForm())
    toast.success('添加成功')
  }

  const handleFilterFormChange = (
    newForm: MessageFilterForm<typeof msgType>,
  ) => {
    setFilterForm(newForm)
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-medium">{title}</h3>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>

      <div className="space-y-2 max-h-[200px] overflow-y-auto border rounded-md p-2">
        {messages.length === 0 ? (
          <div className="text-sm text-center py-4 text-muted-foreground">
            暂无消息
          </div>
        ) : (
          messages.map((message, index) => (
            // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
            <div key={index} className="flex items-center gap-2 group">
              <div className="flex-1 text-sm p-2 rounded bg-muted/50 flex justify-between">
                <span>
                  {typeof message === 'string' ? message : message.content}
                </span>
                {typeof message !== 'string' && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <FilterIcon className="w-4 h-4 text-gray-600" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <FilterText filterConfig={message.filter} />
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => onRemove(index)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))
        )}
      </div>

      <div className="flex items-center gap-2">
        <MessageFilter
          filterForm={filterForm}
          onChange={handleFilterFormChange}
          msgType={msgType}
        />
        <Input
          placeholder={placeholder || '输入回复消息...'}
          value={newMessage}
          onChange={e => setNewMessage(e.target.value)}
          className="flex-1"
        />
        <Button onClick={handleAdd}>添加</Button>
      </div>
    </div>
  )
}

export default ReplyMessageManager
