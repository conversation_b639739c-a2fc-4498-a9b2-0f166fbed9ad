import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { PopoverClose } from '@radix-ui/react-popover'
import {
  FunnelPlusIcon,
  PlusIcon,
  Trash2Icon,
  XIcon,
} from 'lucide-react'
import type { EventMessageType, MessageOf } from '@/hooks/useAutoReply'
import type { StringFilter } from '@/utils/filter'

const conditionTextMap = {
  eq: '等于',
  includes: '包含',
} as const

const fieldNameMap = {
  nick_name: '用户名',
  order_status: '订单状态',
  product_title: '商品名称',
} as const

const getFilterFieldMapping = (msgType: EventMessageType) => {
  switch (msgType) {
    case 'live_order':
      return ['nick_name', 'product_title', 'order_status'] as const
    default:
      return ['nick_name'] as const
  }
}

type MessageFilterForm<T extends EventMessageType> = {
  type: keyof StringFilter
  field: 'nick_name'  // 限制为 nick_name 字段以匹配 ReplyMessageManager
  content: string[]
}

interface MessageFilterProps<T extends EventMessageType> {
  filterForm: MessageFilterForm<T>
  onChange: (newFilterForm: MessageFilterForm<T>) => void
  msgType: T
}

function MessageFilter<T extends EventMessageType>({
  filterForm,
  onChange,
  msgType,
}: MessageFilterProps<T>) {
  const [open, setOpen] = useState(false)

  const currentFieldMapping = getFilterFieldMapping(msgType).map(value => {
    return [value, fieldNameMap[value]]
  })

  // 当打开弹窗时，确保至少有一个输入框
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    if (newOpen && filterForm.content.length === 0) {
      onChange({
        ...filterForm,
        content: ['']
      })
    }
  }

  const handleFilterContentChange = (newValue: string, index: number) => {
    const updated = [...filterForm.content]
    updated[index] = newValue
    onChange({
      ...filterForm,
      content: updated,
    })
  }

  const handleFilterContentAdd = () => {
    onChange({
      ...filterForm,
      content: [...filterForm.content, ''],
    })
  }

  const handleFilterContentRemove = (index: number) => {
    onChange({
      ...filterForm,
      content: filterForm.content.filter((_, i) => i !== index),
    })
  }

  const handleFilterFieldChange = (newFiled: string) => {
    onChange({
      ...filterForm,
      field: newFiled as typeof filterForm.field,
    })
  }

  const handleFilterTypeChange = (newType: string) => {
    onChange({
      ...filterForm,
      type: newType as typeof filterForm.type,
    })
  }

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          className="w-4"
        >
          <FunnelPlusIcon className="w-4 h-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent>
        <div className="">
          <div className="text-sm">过滤器(高级功能)</div>
          <p className="text-sm text-muted-foreground">
            <strong>功能说明：</strong><br />
            针对特定用户或特定内容进行定向回复，提供更个性化的自动回复体验。<br /><br />

            <strong>操作步骤：</strong><br />
            1. 选择过滤字段 2. 选择条件类型 3. 填写过滤内容 4. 添加回复消息
            <br /> <br />

            <strong>使用示例：</strong><br />
            • 用户名包含"张三" → "感谢张三的支持！"<br />
            • 商品名称包含"口红" → "这款口红质地丝滑，颜色持久"<br />
            • 订单状态等于"已付款" → "感谢购买！我们会尽快发货"<br />

          </p>
        </div>
        <div className="grid grid-cols-4 gap-x-1 pt-2">
          <div className="col-span-4 flex flex-col space-y-2">
            <div className="flex space-x-1">
              <Select
                value={filterForm.field as string}
                onValueChange={handleFilterFieldChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择字段" />
                </SelectTrigger>
                <SelectContent>
                  {currentFieldMapping.map(([value, text]) => {
                    return (
                      <SelectItem value={value} key={value}>
                        {text}
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
              <Select
                value={filterForm.type as string}
                onValueChange={handleFilterTypeChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择条件" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(conditionTextMap).map(([value, text]) => {
                    return (
                      <SelectItem value={value} key={value}>
                        {text}
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </div>
            {filterForm.content.map((v, i) => (
              <div
                className="flex space-x-1"
                // biome-ignore lint/suspicious/noArrayIndexKey: 数量少，key 用 index 不要紧的
                key={i}
              >
                <Input
                  placeholder="填写条件（如“张三”）"
                  value={v}
                  onChange={e => handleFilterContentChange(e.target.value, i)}
                />
                <Button
                  variant="ghost"
                  onClick={() => handleFilterContentRemove(i)}
                >
                  <Trash2Icon className="w-4 h-4" />
                </Button>
              </div>
            ))}
            <Button variant="ghost" onClick={handleFilterContentAdd}>
              <PlusIcon className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <PopoverClose className="absolute top-3 right-3">
          <XIcon className="w-4 h-4" onClick={() => handleOpenChange(false)} />
        </PopoverClose>
      </PopoverContent>
    </Popover>
  )
}

export default MessageFilter
