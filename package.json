{"name": "oba-live-tool", "type": "module", "version": "2.0.0", "private": true, "description": "抖音直播工具，用于直播带货，能自动弹窗，自动发言", "author": "OBA Live Streaming Department", "license": "MIT", "main": "dist-electron/main/index.js", "debug": {"env": {"VITE_DEV_SERVER_URL": "http://127.0.0.1:7777/"}}, "scripts": {"dev": "vite", "build": "tsc && vite build && electron-builder --publish never", "preview": "vite preview", "pretest": "vite build --mode=test", "test": "vitest run", "prepare": "husky", "bump": "bumpp --no-commit --no-tag --no-push && changelogen --output", "release": "node ./scripts/release"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-virtual": "^3.13.12", "ahooks": "^3.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.6", "electron-updater": "6.6.2", "highlight.js": "^11.11.1", "immer": "^10.1.1", "lodash-es": "^4.17.21", "lucide-react": "0.522.0", "marked": "^15.0.12", "motion": "^12.6.2", "node-media-server": "^4.0.18", "openai": "5.1.0", "playwright": "^1.50.0", "playwright-extra": "^4.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "react-markdown": "^10.1.0", "react-router": "^7.1.3", "remark-gfm": "^4.0.1", "vaul": "^1.1.2", "ws": "^8.18.1", "zustand": "^5.0.3"}, "devDependencies": {"@biomejs/biome": "2.0.4", "@eslint-react/eslint-plugin": "^1.24.1", "@tailwindcss/postcss": "^4.0.15", "@types/lodash-es": "^4.17.12", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@types/semver": "^7.7.0", "@types/signale": "^1.4.7", "@types/ws": "^8.18.0", "@vitejs/plugin-react": "^4.3.4", "@welldone-software/why-did-you-render": "10.0.1", "builder-util-runtime": "^9.2.10", "bumpp": "^10.0.1", "changelogen": "^0.6.1", "dotenv": "^16.5.0", "electron": "^36.3.2", "electron-builder": "26.0.12", "electron-log": "^5.3.0", "husky": "^9.1.7", "lint-staged": "16.1.0", "postcss": "^8.5.1", "react": "19.1.0", "react-dom": "19.1.0", "rehype-highlight": "^7.0.2", "semver": "^7.7.1", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.15", "tsx": "^4.19.4", "tw-animate-css": "^1.2.4", "typescript": "^5.7.3", "vite": "^6.0.11", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vitest": "^3.0.4"}, "lint-staged": {"*": ["biome check --write --no-errors-on-unmatched --files-ignore-unknown=true"]}, "pnpm": {"onlyBuiltDependencies": ["electron"]}}